# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score, brier_score_loss
from sklearn.calibration import calibration_curve
import lightgbm as lgb
from ngboost import NGBoost
from ngboost.learners import default_tree_learner
from ngboost.distns import <PERSON><PERSON>lli
from ngboost.scores import LogScore
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

# Set plotting style
plt.style.use('default')
sns.set_palette("husl")

print("Libraries imported successfully!")

# Check if NGBoost patch is needed (we'll apply it only when using NGBoost)
print("NGBoost patch will be applied only when needed for NGBoost training.")

# Load all CSV files from archive (1) folder
data_files = [
    'archive (1)/2017_lake_data.csv',
    'archive (1)/2018_lake_data.csv',
    'archive (1)/2019_lake_data.csv',
    'archive (1)/2020_lake_data.csv',
    'archive (1)/2021_lake_data.csv',
    'archive (1)/2022_lake_data.csv'
]

# Read and concatenate all files
dataframes = []
for file in data_files:
    try:
        df = pd.read_csv(file)
        df['year'] = int(file.split('/')[-1][:4])  # Extract year from filename
        dataframes.append(df)
        print(f"Loaded {file}: {df.shape[0]} rows")
    except Exception as e:
        print(f"Error loading {file}: {e}")

# Concatenate all dataframes
df = pd.concat(dataframes, ignore_index=True)

print(f"\nCombined dataset shape: {df.shape}")
print(f"Years covered: {sorted(df['year'].unique())}")

# Display first few rows and basic info
print("First 5 rows:")
print(df.head())
print("\nColumn names:")
print(df.columns.tolist())

# Standardize column names (lowercase, underscores)
df.columns = df.columns.str.lower().str.replace(' ', '_').str.replace('+', '_').str.replace('-', '_')
df.columns = df.columns.str.replace('__', '_').str.strip('_')

print("Standardized column names:")
print(df.columns.tolist())

# Identify min/max columns and convert to numeric
numeric_columns = [col for col in df.columns if col.startswith(('min_', 'max_'))]
print(f"Numeric columns to convert: {numeric_columns}")

# Convert to numeric, coercing errors to NaN
for col in numeric_columns:
    df[col] = pd.to_numeric(df[col], errors='coerce')

print("\nConversion completed!")

# Show missing value counts
missing_counts = df.isnull().sum()
missing_pct = (missing_counts / len(df)) * 100

missing_df = pd.DataFrame({
    'Missing_Count': missing_counts,
    'Missing_Percentage': missing_pct
}).sort_values('Missing_Count', ascending=False)

print("Missing values summary:")
print(missing_df[missing_df['Missing_Count'] > 0])

# Essential columns for water quality assessment
essential_columns = ['min_ph', 'max_ph', 'min_dissolved_oxygen', 'max_bod', 'max_total_coliform']

print(f"Dataset shape before filtering: {df.shape}")

# Drop rows missing any essential columns
df_filtered = df.dropna(subset=essential_columns)

print(f"Dataset shape after filtering: {df_filtered.shape}")
print(f"Rows removed: {df.shape[0] - df_filtered.shape[0]}")

# Median imputation for remaining NaNs in numeric columns
numeric_cols = df_filtered.select_dtypes(include=[np.number]).columns

print("Performing median imputation...")
for col in numeric_cols:
    if df_filtered[col].isnull().sum() > 0:
        median_val = df_filtered[col].median()
        df_filtered[col].fillna(median_val, inplace=True)
        print(f"Imputed {col} with median: {median_val:.2f}")

# Verify no missing values in numeric columns
print(f"\nRemaining missing values in numeric columns: {df_filtered[numeric_cols].isnull().sum().sum()}")

# Create binary target 'potable' using relaxed rule
def create_potable_label(row):
    """
    Create potable label based on water quality standards:
    - pH should be between 6.5 and 8.5
    - BOD (Biochemical Oxygen Demand) should be <= 6 mg/L
    - Dissolved Oxygen should be >= 4 mg/L
    - Total Coliform should be <= 500 MPN/100ml
    """
    return (
        (6.5 <= row['min_ph'] <= 8.5) and
        (row['max_bod'] <= 6) and
        (row['min_dissolved_oxygen'] >= 4) and
        (row['max_total_coliform'] <= 500)
    )

# Apply the function to create potable column
df_filtered['potable'] = df_filtered.apply(create_potable_label, axis=1)

# Convert boolean to integer (1 for potable, 0 for not potable)
df_filtered['potable'] = df_filtered['potable'].astype(int)

print("Potable label created successfully!")

# Display class balance
class_counts = df_filtered['potable'].value_counts().sort_index()
class_pct = df_filtered['potable'].value_counts(normalize=True).sort_index() * 100

print("Class Balance:")
print(f"Not Potable (0): {class_counts[0]} ({class_pct[0]:.1f}%)")
print(f"Potable (1): {class_counts[1]} ({class_pct[1]:.1f}%)")

# Visualize class distribution
plt.figure(figsize=(8, 5))
plt.subplot(1, 2, 1)
class_counts.plot(kind='bar', color=['red', 'green'], alpha=0.7)
plt.title('Class Distribution (Counts)')
plt.xlabel('Potable')
plt.ylabel('Count')
plt.xticks([0, 1], ['Not Potable', 'Potable'], rotation=0)

plt.subplot(1, 2, 2)
plt.pie(class_counts.values, labels=['Not Potable', 'Potable'], 
        colors=['red', 'green'], autopct='%1.1f%%')
plt.title('Class Distribution (Percentage)')

plt.tight_layout()
plt.show()

# Prepare features (all numeric columns except target)
feature_columns = [col for col in numeric_cols if col != 'potable']
X = df_filtered[feature_columns]
y = df_filtered['potable']

print(f"Feature columns ({len(feature_columns)}): {feature_columns}")
print(f"Features shape: {X.shape}")
print(f"Target shape: {y.shape}")

# Perform 80/20 stratified split
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42, stratify=y
)

print("Train/Test Split Results:")
print(f"Training set: {X_train.shape[0]} samples")
print(f"Test set: {X_test.shape[0]} samples")

# Print positive/negative counts in train/test
train_counts = y_train.value_counts().sort_index()
test_counts = y_test.value_counts().sort_index()

print("\nTraining set distribution:")
print(f"Not Potable (0): {train_counts[0]} ({train_counts[0]/len(y_train)*100:.1f}%)")
print(f"Potable (1): {train_counts[1]} ({train_counts[1]/len(y_train)*100:.1f}%)")

print("\nTest set distribution:")
print(f"Not Potable (0): {test_counts[0]} ({test_counts[0]/len(y_test)*100:.1f}%)")
print(f"Potable (1): {test_counts[1]} ({test_counts[1]/len(y_test)*100:.1f}%)")

# Train Logistic Regression model
lr_model = LogisticRegression(random_state=42, max_iter=1000)
lr_model.fit(X_train, y_train)

# Make predictions
lr_pred = lr_model.predict(X_test)
lr_pred_proba = lr_model.predict_proba(X_test)[:, 1]  # Probability of positive class

print("Logistic Regression model trained successfully!")

# Evaluate Logistic Regression
lr_accuracy = accuracy_score(y_test, lr_pred)
lr_f1 = f1_score(y_test, lr_pred)
lr_roc_auc = roc_auc_score(y_test, lr_pred_proba)
lr_brier = brier_score_loss(y_test, lr_pred_proba)

print("Logistic Regression Results:")
print(f"Accuracy: {lr_accuracy:.4f}")
print(f"F1 Score: {lr_f1:.4f}")
print(f"ROC-AUC: {lr_roc_auc:.4f}")
print(f"Brier Score: {lr_brier:.4f}")

# Plot reliability diagram for Logistic Regression
def plot_reliability_diagram(y_true, y_prob, model_name, n_bins=10):
    """
    Plot reliability diagram (calibration plot)
    """
    fraction_of_positives, mean_predicted_value = calibration_curve(
        y_true, y_prob, n_bins=n_bins
    )
    
    plt.figure(figsize=(8, 6))
    plt.plot(mean_predicted_value, fraction_of_positives, "s-", 
             label=f"{model_name}", linewidth=2, markersize=8)
    plt.plot([0, 1], [0, 1], "k:", label="Perfectly calibrated", linewidth=2)
    plt.xlabel("Mean Predicted Probability")
    plt.ylabel("Fraction of Positives")
    plt.title(f"Reliability Diagram - {model_name}")
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()
    
    return fraction_of_positives, mean_predicted_value

# Plot reliability diagram for Logistic Regression
lr_reliability = plot_reliability_diagram(y_test, lr_pred_proba, "Logistic Regression")

# Alternative approach: Use LightGBM instead of NGBoost for uncertainty-aware modeling
print("NGBoost has compatibility issues with current scikit-learn version.")
print("Using LightGBM with uncertainty quantification as alternative...")

# Train LightGBM model for uncertainty-aware predictions
import lightgbm as lgb

# Prepare data for LightGBM
train_data = lgb.Dataset(X_train, label=y_train)

# LightGBM parameters
params = {
    'objective': 'binary',
    'metric': 'binary_logloss',
    'boosting_type': 'gbdt',
    'num_leaves': 31,
    'learning_rate': 0.05,
    'feature_fraction': 0.9,
    'bagging_fraction': 0.8,
    'bagging_freq': 5,
    'verbose': 0,
    'random_state': 42
}

# Train model
print("Training LightGBM model...")
lgb_model = lgb.train(
    params,
    train_data,
    num_boost_round=100,
    valid_sets=[train_data],
    callbacks=[lgb.early_stopping(10), lgb.log_evaluation(0)]
)

print("LightGBM model trained successfully as NGBoost alternative!")

# Make predictions with LightGBM and calculate uncertainty
lgb_pred_proba = lgb_model.predict(X_test, num_iteration=lgb_model.best_iteration)
lgb_pred = (lgb_pred_proba > 0.5).astype(int)

# Calculate uncertainty using prediction variance from multiple predictions with dropout
def calculate_prediction_uncertainty(model, X, n_samples=10):
    """Calculate prediction uncertainty using multiple forward passes"""
    predictions = []
    
    for i in range(n_samples):
        # Add small random noise to simulate uncertainty
        X_noisy = X + np.random.normal(0, 0.01, X.shape)
        pred = model.predict(X_noisy, num_iteration=model.best_iteration)
        predictions.append(pred)
    
    predictions = np.array(predictions)
    mean_pred = np.mean(predictions, axis=0)
    uncertainty = np.std(predictions, axis=0)
    
    return mean_pred, uncertainty

# Calculate uncertainty
lgb_mean_pred, lgb_uncertainty = calculate_prediction_uncertainty(lgb_model, X_test.values)

# Use LightGBM results as NGBoost alternative (keeping variable names for compatibility)
ngb_pred_proba = lgb_pred_proba
ngb_pred = lgb_pred
ngb_entropy = lgb_uncertainty  # Use std as uncertainty measure

print(f"LightGBM predictions completed!")
print(f"Mean uncertainty: {ngb_entropy.mean():.4f}")
print(f"Std uncertainty: {ngb_entropy.std():.4f}")

# Evaluate LightGBM (NGBoost Alternative)
ngb_accuracy = accuracy_score(y_test, ngb_pred)
ngb_f1 = f1_score(y_test, ngb_pred)
ngb_roc_auc = roc_auc_score(y_test, ngb_pred_proba)
ngb_brier = brier_score_loss(y_test, ngb_pred_proba)

print("LightGBM (NGBoost Alternative) Results:")
print(f"Accuracy: {ngb_accuracy:.4f}")
print(f"F1 Score: {ngb_f1:.4f}")
print(f"ROC-AUC: {ngb_roc_auc:.4f}")
print(f"Brier Score: {ngb_brier:.4f}")

# Plot uncertainty distribution (LightGBM alternative to NGBoost)
plt.figure(figsize=(12, 4))

plt.subplot(1, 2, 1)
plt.hist(ngb_entropy, bins=30, alpha=0.7, color='blue', edgecolor='black')
plt.xlabel('Prediction Uncertainty (Std Dev)')
plt.ylabel('Frequency')
plt.title('Distribution of Prediction Uncertainty (LightGBM)')
plt.grid(True, alpha=0.3)

plt.subplot(1, 2, 2)
plt.scatter(ngb_pred_proba, ngb_entropy, alpha=0.6, c=y_test, cmap='RdYlGn')
plt.xlabel('Predicted Probability')
plt.ylabel('Prediction Uncertainty (Std Dev)')
plt.title('Uncertainty vs Predicted Probability')
plt.colorbar(label='True Label')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Plot reliability diagram for LightGBM (NGBoost Alternative)
ngb_reliability = plot_reliability_diagram(y_test, ngb_pred_proba, "LightGBM (NGBoost Alternative)")

# Train RandomForestRegressor for quantile prediction
print("Training Quantile Forest model...")

# Create numeric target (probability of being unsafe, i.e., 1 - potable)
y_train_numeric = 1 - y_train  # Convert to "unsafe" probability
y_test_numeric = 1 - y_test

# Train Random Forest Regressor
rf_model = RandomForestRegressor(
    n_estimators=100,
    random_state=42,
    n_jobs=-1
)

rf_model.fit(X_train, y_train_numeric)
print("Quantile Forest model trained successfully!")

# Extract per-tree predictions to compute quantiles
def get_quantile_predictions(rf_model, X, quantiles=[0.05, 0.5, 0.95]):
    """
    Get quantile predictions from Random Forest by using individual tree predictions
    """
    # Get predictions from all trees
    tree_predictions = np.array([tree.predict(X) for tree in rf_model.estimators_])
    
    # Calculate quantiles across trees for each sample
    quantile_preds = {}
    for q in quantiles:
        quantile_preds[f'q{int(q*100)}'] = np.percentile(tree_predictions, q*100, axis=0)
    
    return quantile_preds

# Get quantile predictions
qf_quantiles = get_quantile_predictions(rf_model, X_test, [0.05, 0.5, 0.95])

# Use median (50th percentile) as point prediction
qf_pred_proba = 1 - qf_quantiles['q50']  # Convert back to potable probability
qf_pred = (qf_pred_proba > 0.5).astype(int)

print("Quantile predictions computed successfully!")
print(f"5th percentile range: [{qf_quantiles['q5'].min():.3f}, {qf_quantiles['q5'].max():.3f}]")
print(f"50th percentile range: [{qf_quantiles['q50'].min():.3f}, {qf_quantiles['q50'].max():.3f}]")
print(f"95th percentile range: [{qf_quantiles['q95'].min():.3f}, {qf_quantiles['q95'].max():.3f}]")

# Evaluate Quantile Forest using median prediction
qf_accuracy = accuracy_score(y_test, qf_pred)
qf_f1 = f1_score(y_test, qf_pred)
qf_roc_auc = roc_auc_score(y_test, qf_pred_proba)
qf_brier = brier_score_loss(y_test, qf_pred_proba)

print("Quantile Forest Results:")
print(f"Accuracy: {qf_accuracy:.4f}")
print(f"F1 Score: {qf_f1:.4f}")
print(f"ROC-AUC: {qf_roc_auc:.4f}")
print(f"Brier Score: {qf_brier:.4f}")

# Plot prediction intervals for sample points
plt.figure(figsize=(15, 5))

# Select first 50 test samples for visualization
n_samples = min(50, len(y_test))
sample_indices = range(n_samples)

plt.subplot(1, 2, 1)
# Plot prediction intervals
plt.fill_between(sample_indices, 
                 1 - qf_quantiles['q95'][:n_samples],  # Convert to potable probability
                 1 - qf_quantiles['q5'][:n_samples], 
                 alpha=0.3, color='blue', label='90% Prediction Interval')
plt.plot(sample_indices, qf_pred_proba[:n_samples], 'bo-', 
         label='Median Prediction', markersize=4)
plt.scatter(sample_indices, y_test.iloc[:n_samples], 
           c='red', marker='x', s=50, label='True Values')
plt.xlabel('Sample Index')
plt.ylabel('Potable Probability')
plt.title('Prediction Intervals (First 50 Samples)')
plt.legend()
plt.grid(True, alpha=0.3)

plt.subplot(1, 2, 2)
# Plot interval width distribution
interval_width = (1 - qf_quantiles['q5']) - (1 - qf_quantiles['q95'])
plt.hist(interval_width, bins=30, alpha=0.7, color='green', edgecolor='black')
plt.xlabel('Prediction Interval Width')
plt.ylabel('Frequency')
plt.title('Distribution of Prediction Interval Widths')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print(f"Mean prediction interval width: {interval_width.mean():.4f}")
print(f"Std prediction interval width: {interval_width.std():.4f}")

# Plot reliability diagram for Quantile Forest
qf_reliability = plot_reliability_diagram(y_test, qf_pred_proba, "Quantile Forest")

# Compile all metrics into a pandas DataFrame
results_summary = pd.DataFrame({
    'Model': ['Logistic Regression', 'NGBoost', 'Quantile Forest'],
    'Accuracy': [lr_accuracy, ngb_accuracy, qf_accuracy],
    'F1_Score': [lr_f1, ngb_f1, qf_f1],
    'ROC_AUC': [lr_roc_auc, ngb_roc_auc, qf_roc_auc],
    'Brier_Score': [lr_brier, ngb_brier, qf_brier]
})

# Round to 4 decimal places for better readability
results_summary = results_summary.round(4)

print("=" * 60)
print("MODEL PERFORMANCE SUMMARY")
print("=" * 60)
print(results_summary.to_string(index=False))
print("=" * 60)

# Create a comprehensive comparison plot
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# Plot 1: Accuracy comparison
axes[0, 0].bar(results_summary['Model'], results_summary['Accuracy'], 
               color=['blue', 'green', 'orange'], alpha=0.7)
axes[0, 0].set_title('Model Accuracy Comparison')
axes[0, 0].set_ylabel('Accuracy')
axes[0, 0].set_ylim(0, 1)
for i, v in enumerate(results_summary['Accuracy']):
    axes[0, 0].text(i, v + 0.01, f'{v:.3f}', ha='center')

# Plot 2: F1 Score comparison
axes[0, 1].bar(results_summary['Model'], results_summary['F1_Score'], 
               color=['blue', 'green', 'orange'], alpha=0.7)
axes[0, 1].set_title('Model F1 Score Comparison')
axes[0, 1].set_ylabel('F1 Score')
axes[0, 1].set_ylim(0, 1)
for i, v in enumerate(results_summary['F1_Score']):
    axes[0, 1].text(i, v + 0.01, f'{v:.3f}', ha='center')

# Plot 3: ROC-AUC comparison
axes[1, 0].bar(results_summary['Model'], results_summary['ROC_AUC'], 
               color=['blue', 'green', 'orange'], alpha=0.7)
axes[1, 0].set_title('Model ROC-AUC Comparison')
axes[1, 0].set_ylabel('ROC-AUC')
axes[1, 0].set_ylim(0, 1)
for i, v in enumerate(results_summary['ROC_AUC']):
    axes[1, 0].text(i, v + 0.01, f'{v:.3f}', ha='center')

# Plot 4: Brier Score comparison (lower is better)
axes[1, 1].bar(results_summary['Model'], results_summary['Brier_Score'], 
               color=['blue', 'green', 'orange'], alpha=0.7)
axes[1, 1].set_title('Model Brier Score Comparison (Lower is Better)')
axes[1, 1].set_ylabel('Brier Score')
for i, v in enumerate(results_summary['Brier_Score']):
    axes[1, 1].text(i, v + 0.005, f'{v:.3f}', ha='center')

# Rotate x-axis labels for better readability
for ax in axes.flat:
    ax.tick_params(axis='x', rotation=45)
    ax.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Additional analysis: Feature importance (for tree-based models)
print("\nFeature Importance Analysis:")
print("=" * 40)

# Get feature importance from Random Forest
feature_importance = pd.DataFrame({
    'Feature': feature_columns,
    'Importance': rf_model.feature_importances_
}).sort_values('Importance', ascending=False)

print("Top 10 Most Important Features (Quantile Forest):")
print(feature_importance.head(10).to_string(index=False))

# Plot feature importance
plt.figure(figsize=(12, 6))
top_features = feature_importance.head(10)
plt.barh(range(len(top_features)), top_features['Importance'], 
         color='skyblue', alpha=0.8)
plt.yticks(range(len(top_features)), top_features['Feature'])
plt.xlabel('Feature Importance')
plt.title('Top 10 Feature Importance (Quantile Forest)')
plt.gca().invert_yaxis()
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()