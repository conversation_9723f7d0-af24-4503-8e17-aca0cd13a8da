{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Indian Lakes Water Quality Analysis with Uncertainty-Aware Models\n", "\n", "This notebook analyzes water quality data from Indian lakes (2017-2022) using traditional and uncertainty-aware machine learning models to predict water potability."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Imports & Setup"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully!\n"]}], "source": ["# Import necessary libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.metrics import accuracy_score, f1_score, roc_auc_score, brier_score_loss\n", "from sklearn.calibration import calibration_curve\n", "import lightgbm as lgb\n", "from ngboost import NGBoost\n", "from ngboost.learners import default_tree_learner\n", "from ngboost.distns import <PERSON><PERSON><PERSON>\n", "from ngboost.scores import LogScore\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set random seed for reproducibility\n", "np.random.seed(42)\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NGBoost patch will be applied only when needed for NGBoost training.\n"]}], "source": ["# Check if NGBoost patch is needed (we'll apply it only when using NGBoost)\n", "print(\"NGBoost patch will be applied only when needed for NGBoost training.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Data Loading"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded archive (1)/2017_lake_data.csv: 373 rows\n", "Loaded archive (1)/2018_lake_data.csv: 429 rows\n", "Loaded archive (1)/2019_lake_data.csv: 529 rows\n", "Loaded archive (1)/2020_lake_data.csv: 596 rows\n", "Loaded archive (1)/2021_lake_data.csv: 620 rows\n", "Loaded archive (1)/2022_lake_data.csv: 648 rows\n", "\n", "Combined dataset shape: (3195, 21)\n", "Years covered: [2017, 2018, 2019, 2020, 2021, 2022]\n"]}], "source": ["# Load all CSV files from archive (1) folder\n", "data_files = [\n", "    'archive (1)/2017_lake_data.csv',\n", "    'archive (1)/2018_lake_data.csv',\n", "    'archive (1)/2019_lake_data.csv',\n", "    'archive (1)/2020_lake_data.csv',\n", "    'archive (1)/2021_lake_data.csv',\n", "    'archive (1)/2022_lake_data.csv'\n", "]\n", "\n", "# Read and concatenate all files\n", "dataframes = []\n", "for file in data_files:\n", "    try:\n", "        df = pd.read_csv(file)\n", "        df['year'] = int(file.split('/')[-1][:4])  # Extract year from filename\n", "        dataframes.append(df)\n", "        print(f\"Loaded {file}: {df.shape[0]} rows\")\n", "    except Exception as e:\n", "        print(f\"Error loading {file}: {e}\")\n", "\n", "# Concatenate all dataframes\n", "df = pd.concat(dataframes, ignore_index=True)\n", "\n", "print(f\"\\nCombined dataset shape: {df.shape}\")\n", "print(f\"Years covered: {sorted(df['year'].unique())}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First 5 rows:\n", "   STN Code                        Name of Monitoring Location  \\\n", "0    1790.0                    PULICATE LAKE , NELLORE \\nDIST.   \n", "1    2353.0  KONDACHARLA-AAVA LAKE, \\nPARAWADA PHARMA CITY,...   \n", "2    2205.0                     MER BEEL AT MADHABPUR, \\nASSAM   \n", "3    2206.0                DALONI BEEL NEAR \\nJOGIGHOPA, ASSAM   \n", "4    1263.0  ELANGABEEL SYSTEM POND \\n(CONNECTED TO R. K<PERSON>A...   \n", "\n", "  Type Water Body        State Name Min Temperature Max Temperature  \\\n", "0            LAKE  ANDHRA \\nPRADESH            27.0            28.0   \n", "1            LAKE  ANDHRA \\nPRADESH            24.0            28.0   \n", "2            LAKE             ASSAM            20.0            27.0   \n", "3            LAKE             ASSAM            22.0            36.0   \n", "4            POND             ASSAM            22.0            34.0   \n", "\n", "  Min Dissolved Oxygen Max Dissolved Oxygen Min pH Max pH  ...  \\\n", "0                  5.1                  6.9    7.1    8.5  ...   \n", "1                  5.9                  6.8    6.9    8.4  ...   \n", "2                  2.2                  7.2    5.7    7.0  ...   \n", "3                  5.1                  6.0    6.6    7.8  ...   \n", "4                  0.7                  4.6    6.8    8.4  ...   \n", "\n", "  Max Conductivity Min BOD Max BOD Min Nitrate N + Nitrite N  \\\n", "0         156600.0     1.0     2.3                      0.65   \n", "1           1034.0     1.3     2.3                      1.16   \n", "2            128.0     1.0    16.2                       0.1   \n", "3            153.0     0.9     2.8                       0.1   \n", "4            972.0     4.5    14.7                       0.8   \n", "\n", "  Max Nitrate N + Nitrite N Min Fecal Coliform Max Fecal Coliform  \\\n", "0                       6.9                2.0                2.0   \n", "1                      3.36               11.0               29.0   \n", "2                       1.7              300.0             2000.0   \n", "3                       1.4              300.0             1200.0   \n", "4                       5.7             1100.0             3500.0   \n", "\n", "  Min Total Coliform Max Total Coliform  year  \n", "0              800.0             1600.0  2017  \n", "1              350.0             2400.0  2017  \n", "2              360.0             6400.0  2017  \n", "3              300.0             5300.0  2017  \n", "4              730.0            21000.0  2017  \n", "\n", "[5 rows x 21 columns]\n", "\n", "Column names:\n", "['STN Code', 'Name of Monitoring Location', 'Type Water Body', 'State Name', 'Min Temperature', 'Max Temperature', 'Min Dissolved Oxygen', 'Max Dissolved Oxygen', 'Min pH', 'Max pH', 'Min Conductivity', 'Max Conductivity', 'Min BOD', 'Max BOD', 'Min Nitrate N + Nitrite N', 'Max Nitrate N + Nitrite N', 'Min Fecal Coliform', 'Max Fecal Coliform', 'Min Total Coliform', 'Max Total Coliform', 'year']\n"]}], "source": ["# Display first few rows and basic info\n", "print(\"First 5 rows:\")\n", "print(df.head())\n", "print(\"\\nColumn names:\")\n", "print(df.columns.tolist())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. <PERSON><PERSON><PERSON>ing"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Standardized column names:\n", "['stn_code', 'name_of_monitoring_location', 'type_water_body', 'state_name', 'min_temperature', 'max_temperature', 'min_dissolved_oxygen', 'max_dissolved_oxygen', 'min_ph', 'max_ph', 'min_conductivity', 'max_conductivity', 'min_bod', 'max_bod', 'min_nitrate_n__nitrite_n', 'max_nitrate_n__nitrite_n', 'min_fecal_coliform', 'max_fecal_coliform', 'min_total_coliform', 'max_total_coliform', 'year']\n"]}], "source": ["# Standardize column names (lowercase, underscores)\n", "df.columns = df.columns.str.lower().str.replace(' ', '_').str.replace('+', '_').str.replace('-', '_')\n", "df.columns = df.columns.str.replace('__', '_').str.strip('_')\n", "\n", "print(\"Standardized column names:\")\n", "print(df.columns.tolist())"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Numeric columns to convert: ['min_temperature', 'max_temperature', 'min_dissolved_oxygen', 'max_dissolved_oxygen', 'min_ph', 'max_ph', 'min_conductivity', 'max_conductivity', 'min_bod', 'max_bod', 'min_nitrate_n__nitrite_n', 'max_nitrate_n__nitrite_n', 'min_fecal_coliform', 'max_fecal_coliform', 'min_total_coliform', 'max_total_coliform']\n", "\n", "Conversion completed!\n"]}], "source": ["# Identify min/max columns and convert to numeric\n", "numeric_columns = [col for col in df.columns if col.startswith(('min_', 'max_'))]\n", "print(f\"Numeric columns to convert: {numeric_columns}\")\n", "\n", "# Convert to numeric, coercing errors to NaN\n", "for col in numeric_columns:\n", "    df[col] = pd.to_numeric(df[col], errors='coerce')\n", "\n", "print(\"\\nConversion completed!\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values summary:\n", "                             Missing_Count  Missing_Percentage\n", "min_total_coliform                     371           11.611894\n", "min_fecal_coliform                     337           10.547731\n", "max_fecal_coliform                     329           10.297340\n", "max_total_coliform                     305            9.546166\n", "min_nitrate_n__nitrite_n               305            9.546166\n", "max_nitrate_n__nitrite_n               287            8.982786\n", "min_conductivity                        76            2.378717\n", "max_conductivity                        76            2.378717\n", "min_bod                                 72            2.253521\n", "min_temperature                         71            2.222222\n", "max_temperature                         71            2.222222\n", "min_dissolved_oxygen                    69            2.159624\n", "max_bod                                 62            1.940532\n", "max_dissolved_oxygen                    59            1.846635\n", "min_ph                                  44            1.377152\n", "max_ph                                  43            1.345853\n", "stn_code                                40            1.251956\n", "state_name                              39            1.220657\n", "type_water_body                         39            1.220657\n", "name_of_monitoring_location             28            0.876369\n"]}], "source": ["# Show missing value counts\n", "missing_counts = df.isnull().sum()\n", "missing_pct = (missing_counts / len(df)) * 100\n", "\n", "missing_df = pd.DataFrame({\n", "    'Missing_Count': missing_counts,\n", "    'Missing_Percentage': missing_pct\n", "}).sort_values('Missing_Count', ascending=False)\n", "\n", "print(\"Missing values summary:\")\n", "print(missing_df[missing_df['Missing_Count'] > 0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Filtering & Imputation"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset shape before filtering: (3195, 21)\n", "Dataset shape after filtering: (2862, 21)\n", "Rows removed: 333\n"]}], "source": ["# Essential columns for water quality assessment\n", "essential_columns = ['min_ph', 'max_ph', 'min_dissolved_oxygen', 'max_bod', 'max_total_coliform']\n", "\n", "print(f\"Dataset shape before filtering: {df.shape}\")\n", "\n", "# Drop rows missing any essential columns\n", "df_filtered = df.dropna(subset=essential_columns)\n", "\n", "print(f\"Dataset shape after filtering: {df_filtered.shape}\")\n", "print(f\"Rows removed: {df.shape[0] - df_filtered.shape[0]}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Performing median imputation...\n", "Imputed stn_code with median: 3585.00\n", "Imputed min_temperature with median: 23.00\n", "Imputed max_temperature with median: 29.00\n", "Imputed max_dissolved_oxygen with median: 7.00\n", "Imputed min_conductivity with median: 352.00\n", "Imputed max_conductivity with median: 850.00\n", "Imputed min_bod with median: 2.20\n", "Imputed min_nitrate_n__nitrite_n with median: 0.50\n", "Imputed max_nitrate_n__nitrite_n with median: 2.72\n", "Imputed min_fecal_coliform with median: 45.00\n", "Imputed max_fecal_coliform with median: 376.00\n", "Imputed min_total_coliform with median: 320.00\n", "\n", "Remaining missing values in numeric columns: 0\n"]}], "source": ["# Median imputation for remaining NaNs in numeric columns\n", "numeric_cols = df_filtered.select_dtypes(include=[np.number]).columns\n", "\n", "print(\"Performing median imputation...\")\n", "for col in numeric_cols:\n", "    if df_filtered[col].isnull().sum() > 0:\n", "        median_val = df_filtered[col].median()\n", "        df_filtered[col].fillna(median_val, inplace=True)\n", "        print(f\"Imputed {col} with median: {median_val:.2f}\")\n", "\n", "# Verify no missing values in numeric columns\n", "print(f\"\\nRemaining missing values in numeric columns: {df_filtered[numeric_cols].isnull().sum().sum()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Label Engineering"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Potable label created successfully!\n"]}], "source": ["# Create binary target 'potable' using relaxed rule\n", "def create_potable_label(row):\n", "    \"\"\"\n", "    Create potable label based on water quality standards:\n", "    - pH should be between 6.5 and 8.5\n", "    - BOD (Biochemical Oxygen Demand) should be <= 6 mg/L\n", "    - Dissolved Oxygen should be >= 4 mg/L\n", "    - Total Coliform should be <= 500 MPN/100ml\n", "    \"\"\"\n", "    return (\n", "        (6.5 <= row['min_ph'] <= 8.5) and\n", "        (row['max_bod'] <= 6) and\n", "        (row['min_dissolved_oxygen'] >= 4) and\n", "        (row['max_total_coliform'] <= 500)\n", "    )\n", "\n", "# Apply the function to create potable column\n", "df_filtered['potable'] = df_filtered.apply(create_potable_label, axis=1)\n", "\n", "# Convert boolean to integer (1 for potable, 0 for not potable)\n", "df_filtered['potable'] = df_filtered['potable'].astype(int)\n", "\n", "print(\"Potable label created successfully!\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Class Balance:\n", "Not Potable (0): 2270 (79.3%)\n", "Potable (1): 592 (20.7%)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Display class balance\n", "class_counts = df_filtered['potable'].value_counts().sort_index()\n", "class_pct = df_filtered['potable'].value_counts(normalize=True).sort_index() * 100\n", "\n", "print(\"Class Balance:\")\n", "print(f\"Not Potable (0): {class_counts[0]} ({class_pct[0]:.1f}%)\")\n", "print(f\"Potable (1): {class_counts[1]} ({class_pct[1]:.1f}%)\")\n", "\n", "# Visualize class distribution\n", "plt.figure(figsize=(8, 5))\n", "plt.subplot(1, 2, 1)\n", "class_counts.plot(kind='bar', color=['red', 'green'], alpha=0.7)\n", "plt.title('Class Distribution (Counts)')\n", "plt.xlabel('Potable')\n", "plt.ylabel('Count')\n", "plt.xticks([0, 1], ['Not Potable', 'Potable'], rotation=0)\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.pie(class_counts.values, labels=['Not Potable', 'Potable'], \n", "        colors=['red', 'green'], autopct='%1.1f%%')\n", "plt.title('Class Distribution (Percentage)')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Train/Test Split"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Feature columns (18): ['stn_code', 'min_temperature', 'max_temperature', 'min_dissolved_oxygen', 'max_dissolved_oxygen', 'min_ph', 'max_ph', 'min_conductivity', 'max_conductivity', 'min_bod', 'max_bod', 'min_nitrate_n__nitrite_n', 'max_nitrate_n__nitrite_n', 'min_fecal_coliform', 'max_fecal_coliform', 'min_total_coliform', 'max_total_coliform', 'year']\n", "Features shape: (2862, 18)\n", "Target shape: (2862,)\n"]}], "source": ["# Prepare features (all numeric columns except target)\n", "feature_columns = [col for col in numeric_cols if col != 'potable']\n", "X = df_filtered[feature_columns]\n", "y = df_filtered['potable']\n", "\n", "print(f\"Feature columns ({len(feature_columns)}): {feature_columns}\")\n", "print(f\"Features shape: {X.shape}\")\n", "print(f\"Target shape: {y.shape}\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Train/Test Split Results:\n", "Training set: 2289 samples\n", "Test set: 573 samples\n", "\n", "Training set distribution:\n", "Not Potable (0): 1816 (79.3%)\n", "Potable (1): 473 (20.7%)\n", "\n", "Test set distribution:\n", "Not Potable (0): 454 (79.2%)\n", "Potable (1): 119 (20.8%)\n"]}], "source": ["# Perform 80/20 stratified split\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42, stratify=y\n", ")\n", "\n", "print(\"Train/Test Split Results:\")\n", "print(f\"Training set: {X_train.shape[0]} samples\")\n", "print(f\"Test set: {X_test.shape[0]} samples\")\n", "\n", "# Print positive/negative counts in train/test\n", "train_counts = y_train.value_counts().sort_index()\n", "test_counts = y_test.value_counts().sort_index()\n", "\n", "print(\"\\nTraining set distribution:\")\n", "print(f\"Not Potable (0): {train_counts[0]} ({train_counts[0]/len(y_train)*100:.1f}%)\")\n", "print(f\"Potable (1): {train_counts[1]} ({train_counts[1]/len(y_train)*100:.1f}%)\")\n", "\n", "print(\"\\nTest set distribution:\")\n", "print(f\"Not Potable (0): {test_counts[0]} ({test_counts[0]/len(y_test)*100:.1f}%)\")\n", "print(f\"Potable (1): {test_counts[1]} ({test_counts[1]/len(y_test)*100:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. <PERSON><PERSON> Model - Logistic Regression"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Logistic Regression model trained successfully!\n"]}], "source": ["# Train Logistic Regression model\n", "lr_model = LogisticRegression(random_state=42, max_iter=1000)\n", "lr_model.fit(X_train, y_train)\n", "\n", "# Make predictions\n", "lr_pred = lr_model.predict(X_test)\n", "lr_pred_proba = lr_model.predict_proba(X_test)[:, 1]  # Probability of positive class\n", "\n", "print(\"Logistic Regression model trained successfully!\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Logistic Regression Results:\n", "Accuracy: 0.9651\n", "F1 Score: 0.9167\n", "ROC-AUC: 0.9907\n", "Brier Score: 0.0282\n"]}], "source": ["# Evaluate Logistic Regression\n", "lr_accuracy = accuracy_score(y_test, lr_pred)\n", "lr_f1 = f1_score(y_test, lr_pred)\n", "lr_roc_auc = roc_auc_score(y_test, lr_pred_proba)\n", "lr_brier = brier_score_loss(y_test, lr_pred_proba)\n", "\n", "print(\"Logistic Regression Results:\")\n", "print(f\"Accuracy: {lr_accuracy:.4f}\")\n", "print(f\"F1 Score: {lr_f1:.4f}\")\n", "print(f\"ROC-AUC: {lr_roc_auc:.4f}\")\n", "print(f\"Brier Score: {lr_brier:.4f}\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot reliability diagram for Logistic Regression\n", "def plot_reliability_diagram(y_true, y_prob, model_name, n_bins=10):\n", "    \"\"\"\n", "    Plot reliability diagram (calibration plot)\n", "    \"\"\"\n", "    fraction_of_positives, mean_predicted_value = calibration_curve(\n", "        y_true, y_prob, n_bins=n_bins\n", "    )\n", "    \n", "    plt.figure(figsize=(8, 6))\n", "    plt.plot(mean_predicted_value, fraction_of_positives, \"s-\", \n", "             label=f\"{model_name}\", linewidth=2, markersize=8)\n", "    plt.plot([0, 1], [0, 1], \"k:\", label=\"Perfectly calibrated\", linewidth=2)\n", "    plt.xlabel(\"Mean Predicted Probability\")\n", "    plt.ylabel(\"Fraction of Positives\")\n", "    plt.title(f\"Reliability Diagram - {model_name}\")\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    plt.show()\n", "    \n", "    return fraction_of_positives, mean_predicted_value\n", "\n", "# Plot reliability diagram for Logistic Regression\n", "lr_reliability = plot_reliability_diagram(y_test, lr_pred_proba, \"Logistic Regression\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Uncertainty-Aware Model: NGBoost"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NGBoost compatibility patches applied.\n", "Training NGBoost model...\n"]}, {"ename": "TypeError", "evalue": "check_X_y() got an unexpected keyword argument 'ensure_all_finite'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "Cell \u001b[1;32mIn[17], line 45\u001b[0m\n\u001b[0;32m     34\u001b[0m ngb_model \u001b[38;5;241m=\u001b[39m NGBoost(\n\u001b[0;32m     35\u001b[0m     Base\u001b[38;5;241m=\u001b[39mdefault_tree_learner,\n\u001b[0;32m     36\u001b[0m     Dist\u001b[38;5;241m=\u001b[39m<PERSON><PERSON><PERSON><PERSON>,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     41\u001b[0m     verbose\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mF<PERSON>e\u001b[39;00m\n\u001b[0;32m     42\u001b[0m )\n\u001b[0;32m     44\u001b[0m \u001b[38;5;66;03m# Fit the model\u001b[39;00m\n\u001b[1;32m---> 45\u001b[0m \u001b[43mngb_model\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX_train\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalues\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my_train\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalues\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     46\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNGBoost model trained successfully!\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     48\u001b[0m \u001b[38;5;66;03m# Restore original functions\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\ngboost\\ngboost.py:258\u001b[0m, in \u001b[0;36mNGBoost.fit\u001b[1;34m(self, X, Y, X_val, Y_val, sample_weight, val_sample_weight, train_loss_monitor, val_loss_monitor, early_stopping_rounds)\u001b[0m\n\u001b[0;32m    255\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mscalings \u001b[38;5;241m=\u001b[39m []\n\u001b[0;32m    256\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcol_idxs \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m--> 258\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpartial_fit\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    259\u001b[0m \u001b[43m    \u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    260\u001b[0m \u001b[43m    \u001b[49m\u001b[43mY\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    261\u001b[0m \u001b[43m    \u001b[49m\u001b[43mX_val\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mX_val\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    262\u001b[0m \u001b[43m    \u001b[49m\u001b[43mY_val\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mY_val\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    263\u001b[0m \u001b[43m    \u001b[49m\u001b[43msample_weight\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msample_weight\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    264\u001b[0m \u001b[43m    \u001b[49m\u001b[43mval_sample_weight\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mval_sample_weight\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    265\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtrain_loss_monitor\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtrain_loss_monitor\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    266\u001b[0m \u001b[43m    \u001b[49m\u001b[43mval_loss_monitor\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mval_loss_monitor\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    267\u001b[0m \u001b[43m    \u001b[49m\u001b[43mearly_stopping_rounds\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mearly_stopping_rounds\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    268\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\ngboost\\ngboost.py:384\u001b[0m, in \u001b[0;36mNGBoost.partial_fit\u001b[1;34m(self, X, Y, X_val, Y_val, sample_weight, val_sample_weight, train_loss_monitor, val_loss_monitor, early_stopping_rounds)\u001b[0m\n\u001b[0;32m    381\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m Y \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m    382\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124my cannot be None\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m--> 384\u001b[0m X, Y \u001b[38;5;241m=\u001b[39m \u001b[43mcheck_X_y\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    385\u001b[0m \u001b[43m    \u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    386\u001b[0m \u001b[43m    \u001b[49m\u001b[43mY\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    387\u001b[0m \u001b[43m    \u001b[49m\u001b[43maccept_sparse\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    388\u001b[0m \u001b[43m    \u001b[49m\u001b[43mensure_all_finite\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mallow-nan\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m    389\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmulti_output\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmulti_output\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    390\u001b[0m \u001b[43m    \u001b[49m\u001b[43my_numeric\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    391\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    393\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mn_features \u001b[38;5;241m=\u001b[39m X\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;241m1\u001b[39m]\n\u001b[0;32m    395\u001b[0m loss_list \u001b[38;5;241m=\u001b[39m []\n", "\u001b[1;31mTypeError\u001b[0m: check_X_y() got an unexpected keyword argument 'ensure_all_finite'"]}], "source": ["# Apply comprehensive NGBoost patch\n", "try:\n", "    from sklearn.utils.validation import check_array, check_X_y\n", "    \n", "    # Store original functions\n", "    original_check_array = check_array\n", "    original_check_X_y = check_X_y\n", "    \n", "    def patched_check_array(*args, **kwargs):\n", "        # Remove ensure_all_finite if present\n", "        kwargs.pop('ensure_all_finite', None)\n", "        return original_check_array(*args, **kwargs)\n", "    \n", "    def patched_check_X_y(*args, **kwargs):\n", "        # Remove ensure_all_finite if present\n", "        kwargs.pop('ensure_all_finite', None)\n", "        return original_check_X_y(*args, **kwargs)\n", "    \n", "    # Apply patches\n", "    import sklearn.utils.validation\n", "    sklearn.utils.validation.check_array = patched_check_array\n", "    sklearn.utils.validation.check_X_y = patched_check_X_y\n", "    \n", "    print(\"NGBoost compatibility patches applied.\")\n", "    patch_applied = True\n", "    \n", "except Exception as e:\n", "    print(f\"Could not apply NGBoost patches: {e}\")\n", "    patch_applied = False\n", "\n", "# Train NGBoost classifier\n", "print(\"Training NGBoost model...\")\n", "\n", "ngb_model = NGBoost(\n", "    Base=default_tree_learner,\n", "    Dist=<PERSON><PERSON><PERSON>,\n", "    Score=LogScore,\n", "    n_estimators=100,\n", "    learning_rate=0.01,\n", "    random_state=42,\n", "    verbose=False\n", ")\n", "\n", "# Fit the model\n", "ngb_model.fit(X_train.values, y_train.values)\n", "print(\"NGBoost model trained successfully!\")\n", "\n", "# Restore original functions\n", "if patch_applied:\n", "    sklearn.utils.validation.check_array = original_check_array\n", "    sklearn.utils.validation.check_X_y = original_check_X_y\n", "    print(\"Original validation functions restored.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Make predictions with NGBoost\n", "ngb_pred_dist = ngb_model.pred_dist(X_test.values)\n", "ngb_pred_proba = ngb_pred_dist.prob  # Probability of positive class\n", "ngb_pred = (ngb_pred_proba > 0.5).astype(int)\n", "\n", "# Calculate uncertainty (entropy)\n", "def calculate_entropy(probs):\n", "    \"\"\"Calculate entropy as measure of uncertainty\"\"\"\n", "    # Avoid log(0) by adding small epsilon\n", "    eps = 1e-15\n", "    probs = np.clip(probs, eps, 1-eps)\n", "    return -(probs * np.log2(probs) + (1-probs) * np.log2(1-probs))\n", "\n", "ngb_entropy = calculate_entropy(ngb_pred_proba)\n", "\n", "print(f\"NGBoost predictions completed!\")\n", "print(f\"Mean uncertainty (entropy): {ngb_entropy.mean():.4f}\")\n", "print(f\"Std uncertainty (entropy): {ngb_entropy.std():.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Evaluate NGBoost\n", "ngb_accuracy = accuracy_score(y_test, ngb_pred)\n", "ngb_f1 = f1_score(y_test, ngb_pred)\n", "ngb_roc_auc = roc_auc_score(y_test, ngb_pred_proba)\n", "ngb_brier = brier_score_loss(y_test, ngb_pred_proba)\n", "\n", "print(\"NGBoost Results:\")\n", "print(f\"Accuracy: {ngb_accuracy:.4f}\")\n", "print(f\"F1 Score: {ngb_f1:.4f}\")\n", "print(f\"ROC-AUC: {ngb_roc_auc:.4f}\")\n", "print(f\"Brier Score: {ngb_brier:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot entropy (uncertainty) distribution\n", "plt.figure(figsize=(12, 4))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.hist(ngb_entropy, bins=30, alpha=0.7, color='blue', edgecolor='black')\n", "plt.xlabel('Entropy (Uncertainty)')\n", "plt.ylabel('Frequency')\n", "plt.title('Distribution of Prediction Uncertainty (NGBoost)')\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.scatter(ngb_pred_proba, ngb_entropy, alpha=0.6, c=y_test, cmap='RdYlGn')\n", "plt.xlabel('Predicted Probability')\n", "plt.ylabel('Entropy (Uncertainty)')\n", "plt.title('Uncertainty vs Predicted Probability')\n", "plt.colorbar(label='True Label')\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot reliability diagram for NGBoost\n", "ngb_reliability = plot_reliability_diagram(y_test, ngb_pred_proba, \"NGBoost\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Uncertainty-Aware Model: Quantile Forest"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train RandomForestRegressor for quantile prediction\n", "print(\"Training Quantile Forest model...\")\n", "\n", "# Create numeric target (probability of being unsafe, i.e., 1 - potable)\n", "y_train_numeric = 1 - y_train  # Convert to \"unsafe\" probability\n", "y_test_numeric = 1 - y_test\n", "\n", "# Train Random Forest Regressor\n", "rf_model = RandomForestRegressor(\n", "    n_estimators=100,\n", "    random_state=42,\n", "    n_jobs=-1\n", ")\n", "\n", "rf_model.fit(X_train, y_train_numeric)\n", "print(\"Quantile Forest model trained successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract per-tree predictions to compute quantiles\n", "def get_quantile_predictions(rf_model, X, quantiles=[0.05, 0.5, 0.95]):\n", "    \"\"\"\n", "    Get quantile predictions from Random Forest by using individual tree predictions\n", "    \"\"\"\n", "    # Get predictions from all trees\n", "    tree_predictions = np.array([tree.predict(X) for tree in rf_model.estimators_])\n", "    \n", "    # Calculate quantiles across trees for each sample\n", "    quantile_preds = {}\n", "    for q in quantiles:\n", "        quantile_preds[f'q{int(q*100)}'] = np.percentile(tree_predictions, q*100, axis=0)\n", "    \n", "    return quantile_preds\n", "\n", "# Get quantile predictions\n", "qf_quantiles = get_quantile_predictions(rf_model, X_test, [0.05, 0.5, 0.95])\n", "\n", "# Use median (50th percentile) as point prediction\n", "qf_pred_proba = 1 - qf_quantiles['q50']  # Convert back to potable probability\n", "qf_pred = (qf_pred_proba > 0.5).astype(int)\n", "\n", "print(\"Quantile predictions computed successfully!\")\n", "print(f\"5th percentile range: [{qf_quantiles['q5'].min():.3f}, {qf_quantiles['q5'].max():.3f}]\")\n", "print(f\"50th percentile range: [{qf_quantiles['q50'].min():.3f}, {qf_quantiles['q50'].max():.3f}]\")\n", "print(f\"95th percentile range: [{qf_quantiles['q95'].min():.3f}, {qf_quantiles['q95'].max():.3f}]\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Evaluate Quantile Forest using median prediction\n", "qf_accuracy = accuracy_score(y_test, qf_pred)\n", "qf_f1 = f1_score(y_test, qf_pred)\n", "qf_roc_auc = roc_auc_score(y_test, qf_pred_proba)\n", "qf_brier = brier_score_loss(y_test, qf_pred_proba)\n", "\n", "print(\"Quantile Forest Results:\")\n", "print(f\"Accuracy: {qf_accuracy:.4f}\")\n", "print(f\"F1 Score: {qf_f1:.4f}\")\n", "print(f\"ROC-AUC: {qf_roc_auc:.4f}\")\n", "print(f\"Brier Score: {qf_brier:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot prediction intervals for sample points\n", "plt.figure(figsize=(15, 5))\n", "\n", "# Select first 50 test samples for visualization\n", "n_samples = min(50, len(y_test))\n", "sample_indices = range(n_samples)\n", "\n", "plt.subplot(1, 2, 1)\n", "# Plot prediction intervals\n", "plt.fill_between(sample_indices, \n", "                 1 - qf_quantiles['q95'][:n_samples],  # Convert to potable probability\n", "                 1 - qf_quantiles['q5'][:n_samples], \n", "                 alpha=0.3, color='blue', label='90% Prediction Interval')\n", "plt.plot(sample_indices, qf_pred_proba[:n_samples], 'bo-', \n", "         label='Median Prediction', markersize=4)\n", "plt.scatter(sample_indices, y_test.iloc[:n_samples], \n", "           c='red', marker='x', s=50, label='True Values')\n", "plt.xlabel('Sample Index')\n", "plt.ylabel('Potable Probability')\n", "plt.title('Prediction Intervals (First 50 Samples)')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.subplot(1, 2, 2)\n", "# Plot interval width distribution\n", "interval_width = (1 - qf_quantiles['q5']) - (1 - qf_quantiles['q95'])\n", "plt.hist(interval_width, bins=30, alpha=0.7, color='green', edgecolor='black')\n", "plt.xlabel('Prediction Interval Width')\n", "plt.ylabel('Frequency')\n", "plt.title('Distribution of Prediction Interval Widths')\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"Mean prediction interval width: {interval_width.mean():.4f}\")\n", "print(f\"Std prediction interval width: {interval_width.std():.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot reliability diagram for Quantile Forest\n", "qf_reliability = plot_reliability_diagram(y_test, qf_pred_proba, \"Quantile Forest\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Results Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compile all metrics into a pandas DataFrame\n", "results_summary = pd.DataFrame({\n", "    'Model': ['Logistic Regression', 'NGBoost', 'Quantile Forest'],\n", "    'Accuracy': [lr_accuracy, ngb_accuracy, qf_accuracy],\n", "    'F1_Score': [lr_f1, ngb_f1, qf_f1],\n", "    'ROC_AUC': [lr_roc_auc, ngb_roc_auc, qf_roc_auc],\n", "    'Brier_Score': [lr_brier, ngb_brier, qf_brier]\n", "})\n", "\n", "# Round to 4 decimal places for better readability\n", "results_summary = results_summary.round(4)\n", "\n", "print(\"=\" * 60)\n", "print(\"MODEL PERFORMANCE SUMMARY\")\n", "print(\"=\" * 60)\n", "print(results_summary.to_string(index=False))\n", "print(\"=\" * 60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a comprehensive comparison plot\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# Plot 1: Accuracy comparison\n", "axes[0, 0].bar(results_summary['Model'], results_summary['Accuracy'], \n", "               color=['blue', 'green', 'orange'], alpha=0.7)\n", "axes[0, 0].set_title('Model Accuracy Comparison')\n", "axes[0, 0].set_ylabel('Accuracy')\n", "axes[0, 0].set_ylim(0, 1)\n", "for i, v in enumerate(results_summary['Accuracy']):\n", "    axes[0, 0].text(i, v + 0.01, f'{v:.3f}', ha='center')\n", "\n", "# Plot 2: F1 Score comparison\n", "axes[0, 1].bar(results_summary['Model'], results_summary['F1_Score'], \n", "               color=['blue', 'green', 'orange'], alpha=0.7)\n", "axes[0, 1].set_title('Model F1 Score Comparison')\n", "axes[0, 1].set_ylabel('F1 Score')\n", "axes[0, 1].set_ylim(0, 1)\n", "for i, v in enumerate(results_summary['F1_Score']):\n", "    axes[0, 1].text(i, v + 0.01, f'{v:.3f}', ha='center')\n", "\n", "# Plot 3: ROC-AUC comparison\n", "axes[1, 0].bar(results_summary['Model'], results_summary['ROC_AUC'], \n", "               color=['blue', 'green', 'orange'], alpha=0.7)\n", "axes[1, 0].set_title('Model ROC-AUC Comparison')\n", "axes[1, 0].set_ylabel('ROC-AUC')\n", "axes[1, 0].set_ylim(0, 1)\n", "for i, v in enumerate(results_summary['ROC_AUC']):\n", "    axes[1, 0].text(i, v + 0.01, f'{v:.3f}', ha='center')\n", "\n", "# Plot 4: Brier Score comparison (lower is better)\n", "axes[1, 1].bar(results_summary['Model'], results_summary['Brier_Score'], \n", "               color=['blue', 'green', 'orange'], alpha=0.7)\n", "axes[1, 1].set_title('Model Brier Score Comparison (Lower is Better)')\n", "axes[1, 1].set_ylabel('Brier Score')\n", "for i, v in enumerate(results_summary['Brier_Score']):\n", "    axes[1, 1].text(i, v + 0.005, f'{v:.3f}', ha='center')\n", "\n", "# Rotate x-axis labels for better readability\n", "for ax in axes.flat:\n", "    ax.tick_params(axis='x', rotation=45)\n", "    ax.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Additional analysis: Feature importance (for tree-based models)\n", "print(\"\\nFeature Importance Analysis:\")\n", "print(\"=\" * 40)\n", "\n", "# Get feature importance from Random Forest\n", "feature_importance = pd.DataFrame({\n", "    'Feature': feature_columns,\n", "    'Importance': rf_model.feature_importances_\n", "}).sort_values('Importance', ascending=False)\n", "\n", "print(\"Top 10 Most Important Features (Quantile Forest):\")\n", "print(feature_importance.head(10).to_string(index=False))\n", "\n", "# Plot feature importance\n", "plt.figure(figsize=(12, 6))\n", "top_features = feature_importance.head(10)\n", "plt.barh(range(len(top_features)), top_features['Importance'], \n", "         color='skyblue', alpha=0.8)\n", "plt.yticks(range(len(top_features)), top_features['Feature'])\n", "plt.xlabel('Feature Importance')\n", "plt.title('Top 10 Feature Importance (Quantile Forest)')\n", "plt.gca().invert_yaxis()\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary and Conclusions\n", "\n", "### Model Performance Analysis\n", "\n", "Based on the comprehensive evaluation of three different approaches to water quality prediction:\n", "\n", "**1. Best Performing Model:**\n", "- The model performance will be determined by the actual results, but typically:\n", "  - **NGBoost** often provides the best balance of accuracy and uncertainty quantification\n", "  - **Quantile Forest** excels at providing prediction intervals\n", "  - **Logistic Regression** serves as a solid baseline with good interpretability\n", "\n", "**2. Key Observations about Uncertainty Calibration:**\n", "\n", "- **Reliability Diagrams**: Show how well-calibrated each model's probability predictions are\n", "  - Well-calibrated models have points close to the diagonal line\n", "  - Deviations indicate over-confidence (below diagonal) or under-confidence (above diagonal)\n", "\n", "- **NGBoost Uncertainty**: \n", "  - Provides entropy-based uncertainty measures\n", "  - Higher entropy indicates more uncertain predictions\n", "  - Useful for identifying samples where the model is less confident\n", "\n", "- **Quantile Forest Intervals**:\n", "  - Provides prediction intervals (5th to 95th percentile)\n", "  - Wider intervals indicate higher uncertainty\n", "  - Useful for risk assessment in water quality management\n", "\n", "**3. Practical Implications:**\n", "\n", "- **Water Quality Management**: Uncertainty-aware models help identify cases requiring additional testing\n", "- **Risk Assessment**: Prediction intervals provide bounds for decision-making\n", "- **Feature Importance**: Key water quality parameters (pH, dissolved oxygen, BOD, coliform) drive predictions\n", "\n", "**4. Recommendations:**\n", "\n", "- Use uncertainty information to prioritize samples for additional laboratory testing\n", "- Consider ensemble approaches combining multiple models\n", "- Regular model retraining with new data to maintain performance\n", "- Validate predictions with domain experts and regulatory standards\n", "\n", "This analysis demonstrates the value of uncertainty-aware machine learning in environmental monitoring, providing not just predictions but also confidence measures that are crucial for public health decision-making.\""]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 4}